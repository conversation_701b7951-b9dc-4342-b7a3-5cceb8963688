"use server";

import nodemailer from "nodemailer";
import dns from "dns";

export const sendEmail = async (to: string, subject?: string, text?: string, secure: boolean = false) => {
  if (!process.env.MAIL_HOST)
    throw new Error("Missing MAIL_HOST environment variable. Set MAIL_HOST to your SMTP server (e.g. smtp.gmail.com or mailtrap.io)");
  if (!process.env.MAIL_USER || !process.env.MAIL_PASS)
    throw new Error("Missing MAIL_USER or MAIL_PASS environment variables. Provide SMTP credentials.");
  try {
    await dns.promises.lookup(process.env.MAIL_HOST!);
  } catch (err: any) {
    throw err;
  }

  const transporter = nodemailer.createTransport({
    host: process.env.MAIL_HOST,
    port: Number(process.env.MAIL_PORT) || (secure ? 465 : 587),
    secure: secure,
    auth: {
      user: process.env.MAIL_USER,
      pass: process.env.MAIL_PASS,
    },
    tls: { rejectUnauthorized: false },
    connectionTimeout: 10_000,
  });

  try {
    await transporter.sendMail({
      from: `${process.env.MAIL_USER}@${process.env.MAIL_DOMAIN}>`,
      to: to,
      subject: subject,
      text: text,
    });
  } catch (err: any) {
    throw err;
  }
};

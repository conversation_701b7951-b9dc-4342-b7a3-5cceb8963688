"use client";

import { sendEmail } from "@/server/email";
import { Button } from "../ui/button";
import { toast } from "../ui/toast";

export default () => {
  return (
    <Button
      onClick={async () => {
        try {
          await sendEmail("lucaschy<PERSON>@gmail.com", "Test Email", "This is a test email from My App.");
          toast.success("Mail was successfully sent");
        } catch (e: any) {
          toast.error(e.message);
        }
      }}>
      Try Component
    </Button>
  );
};
